<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Best Sellers</title>
    <style>
        .best-sellers {
            background: #FCF7F2;
            padding: 2.604vw 1.563vw 2.813vw 1.563vw; /* 50px 30px 54px 30px based on 1920px */
        }
        .best-sellers .section-title {
            font-family: 'Playfair Display', serif;
            font-weight: 400;
            font-style: normal;
            font-size: 1.354vw; /* 26px based on 1920px */
            line-height: 100%;
            letter-spacing: 0px;
            margin-bottom: 1.771vw; /* 34px based on 1920px */
            margin-left: 3.646vw; /* 70px based on 1920px */
        }


        .best-sellers .carousel-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .best-sellers .carousel-container {
            display: flex;
            align-items: center;
            overflow: hidden;
            scroll-behavior: smooth;
            gap: 1.563vw; /* 30px based on 1920px */
            flex: 1;
            width: calc(4 * 23.438vw + 3 * 1.563vw); /* 4个产品(450px) + 3个间距(30px) based on 1920px */
            transition: transform 0.3s ease;
        }

        .best-sellers .carousel-arrow {
            background: #6D4C41;
            border: none;
            border-radius: 50%;
            width: 2.083vw; /* 40px based on 1920px */
            height: 2.083vw; /* 40px based on 1920px */
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            font-size: 0.938vw; /* 18px based on 1920px */
            transition: all 0.3s ease;
            z-index: 10;
        }

        .best-sellers .carousel-arrow:hover {
            background: #5D4037;
            transform: scale(1.1);
        }

        .best-sellers .carousel-arrow:disabled {
            background: #CCCCCC;
            cursor: not-allowed;
            transform: none;
        }

        .best-sellers .carousel-arrow.prev {
            margin-right: 1.615vw; /* 31px based on 1920px */
        }

        .best-sellers .carousel-arrow.next {
            margin-left: 1.615vw; /* 31px based on 1920px */
        }

        .best-sellers .carousel-arrow.hidden {
            visibility: hidden;
        }

        .best-sellers .product-card {
            flex: 0 0 21.198vw; /* 450px based on 1920px */
            border-radius: 0.625vw; /* 12px based on 1920px */
            text-align: center;
            position: relative;
            transition: transform 0.3s ease;
        }

        .best-sellers .product-card a {
            text-decoration: none;
            cursor: pointer;
        }

        .best-sellers .product-card img {
            width: 21.198vw; /* 450px based on 1920px */
            height:  23.438vw; /* 407px based on 1920px */
            border-radius: 1.042vw; /* 20px based on 1920px */
            transition: transform 0.3s ease;
            object-fit: cover;
        }

        .best-sellers .product-card a:hover img {
            transform: scale(1.05);
        }

        .best-sellers .product-name-link {
            text-decoration: none;
            color: inherit;
            display: block;
            cursor: pointer;
        }

        .best-sellers .product-name {
            margin-top: 1.563vw; /* 30px based on 1920px */
            color: #555;
            font-family: PingFang SC;
            font-weight: 400;
            font-style: Regular;
            font-size: 0.729vw; /* 14px based on 1920px */
            leading-trim: NONE;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
        }

        .best-sellers .product-name-link:hover .product-name {
            color: #6D4C41;
        }

        .best-sellers .price {
            margin: 0.417vw 0; /* 8px based on 1920px */
            font-family: PingFang SC;
            font-weight: 500;
            font-style: Medium;
            font-size: 0.729vw; /* 14px based on 1920px */
            leading-trim: NONE;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            color: #333333; /* 正常价格颜色 */
        }

        .best-sellers .old-price {
            text-decoration: line-through;
            color: #999999; /* 以前价格颜色 */
            margin-right: 0.417vw; /* 8px based on 1920px */
        }

        .best-sellers .price .discount-price {
            color: #EB5E30; /* 折扣价颜色 */
        }

        .best-sellers .badge-sale {
            position: absolute;
            top: 1.042vw; /* 20 based on 1920px */
            left: 1.042vw; /* 20 based on 1920px */
            background: #EB5E30;
            color: #fff;
            width: 2.552vw; /* 49px based on 1920px */
            height: 1.302vw; /* 25px based on 1920px */
            border-radius: 0.677vw; /* 13px based on 1920px */
            font-family: PingFang SC;
            font-weight: 600;
            font-style: Semibold;
            font-size: 0.729vw; /* 14px based on 1920px */
            leading-trim: NONE;
            line-height: 100%;
            letter-spacing: 0px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .best-sellers .badge-discount {
            position: absolute;
            top: 1.042vw; /* 20 based on 1920px */
            left: 3.854vw; /* 74px (20px + 49px + 5px) based on 1920px */
            background: #D1793E;
            color: #fff;
            width: 2.552vw; /* 49px based on 1920px */
            height: 1.302vw; /* 25px based on 1920px */
            border-radius: 0.677vw; /* 13px based on 1920px */
            font-family: PingFang SC;
            font-weight: 600;
            font-style: Semibold;
            font-size: 0.729vw; /* 14px based on 1920px */
            leading-trim: NONE;
            line-height: 100%;
            letter-spacing: 0px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .best-sellers .btn {
            background: #5c4033;
            color: #F3E8DD;
            width: 13.75vw; /* 264px based on 1920px */
            height: 2.292vw; /* 44px based on 1920px */
            border: none;
            border-radius: 3.125vw; /* 60px based on 1920px */
            cursor: pointer;
            opacity: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            margin: 0 auto;
        }

        @media (max-width: 768px) {
            .best-sellers {
                padding: 6.51vw 3.906vw 7.031vw 3.906vw; /* 50px 30px 54px 30px based on 768px */
            }

            .best-sellers .section-title {
                font-size: 3.385vw; /* 26px based on 768px */
                margin-bottom: 4.427vw; /* 34px based on 768px */
                margin-left: 9.115vw; /* 70px based on 768px */
            }

            .best-sellers .carousel-container {
                gap: 3.906vw; /* 30px based on 768px */
                width: calc(4 * 58.594vw + 3 * 3.906vw); /* 4个产品(450px) + 3个间距(30px) based on 768px */
            }

            .best-sellers .carousel-arrow {
                width: 5.208vw; /* 40px based on 768px */
                height: 5.208vw; /* 40px based on 768px */
                font-size: 2.344vw; /* 18px based on 768px */
            }

            .best-sellers .carousel-arrow.prev {
                margin-right: 4.036vw; /* 31px based on 768px */
            }

            .best-sellers .carousel-arrow.next {
                margin-left: 4.036vw; /* 31px based on 768px */
            }

            .best-sellers .product-card {
                flex: 0 0 58.594vw; /* 450px based on 768px */
                border-radius: 1.563vw; /* 12px based on 768px */
            }

            .best-sellers .product-card img {
                width: 58.594vw; /* 450px based on 768px */
                height: 53.003vw; /* 407px based on 768px */
                border-radius: 2.604vw; /* 20px based on 768px */
                object-fit: cover;
            }

            .best-sellers .product-name {
                margin-top: 3.906vw; /* 30px based on 768px */
                font-size: 1.823vw; /* 14px based on 768px */
            }

            .best-sellers .price {
                margin: 1.042vw 0; /* 8px based on 768px */
                font-size: 1.823vw; /* 14px based on 768px */
            }

            .best-sellers .old-price {
                margin-right: 1.042vw; /* 8px based on 768px */
            }

            .best-sellers .badge-sale {
                top: 1.302vw; /* 10px based on 768px */
                left: 1.302vw; /* 10px based on 768px */
                width: 6.38vw; /* 49px based on 768px */
                height: 3.255vw; /* 25px based on 768px */
                border-radius: 1.693vw; /* 13px based on 768px */
                font-size: 1.823vw; /* 14px based on 768px */
            }

            .best-sellers .badge-discount {
                top: 1.302vw; /* 10px based on 768px */
                left: 8.333vw; /* 64px (10px + 49px + 5px) based on 768px */
                width: 6.38vw; /* 49px based on 768px */
                height: 3.255vw; /* 25px based on 768px */
                border-radius: 1.693vw; /* 13px based on 768px */
                font-size: 1.823vw; /* 14px based on 768px */
            }

            .best-sellers .btn {
                width: 34.375vw; /* 264px based on 768px */
                height: 5.729vw; /* 44px based on 768px */
                border-radius: 7.813vw; /* 60px based on 768px */
                gap: 1.302vw; /* 10px based on 768px */
                margin: 0 auto;
            }
        }

        @media (max-width: 480px) {
            .best-sellers {
                padding: 13.333vw 8vw 14.4vw 8vw; /* 50px 30px 54px 30px based on 375px */
            }

            .best-sellers .section-title {
                font-size: 6.933vw; /* 26px based on 375px */
                margin-bottom: 9.067vw; /* 34px based on 375px */
                margin-left: 18.667vw; /* 70px based on 375px */
            }

            .best-sellers .carousel-container {
                gap: 8vw; /* 30px based on 375px */
                width: calc(4 * 80vw + 3 * 8vw); /* 4个产品(300px) + 3个间距(30px) based on 375px */
            }

            .best-sellers .carousel-arrow {
                width: 10.667vw; /* 40px based on 375px */
                height: 10.667vw; /* 40px based on 375px */
                font-size: 4.8vw; /* 18px based on 375px */
            }

            .best-sellers .carousel-arrow.prev {
                margin-right: 8.267vw; /* 31px based on 375px */
            }

            .best-sellers .carousel-arrow.next {
                margin-left: 8.267vw; /* 31px based on 375px */
            }

            .best-sellers .product-card {
                flex: 0 0 80vw; /* 300px based on 375px */
                border-radius: 3.2vw; /* 12px based on 375px */
            }

            .best-sellers .product-card img {
                width: 80vw; /* 300px based on 375px */
                height: 72.267vw; /* 271px (300px * 407/450) based on 375px */
                border-radius: 5.333vw; /* 20px based on 375px */
                object-fit: cover;
            }

            .best-sellers .product-name {
                margin-top: 8vw; /* 30px based on 375px */
                font-size: 3.733vw; /* 14px based on 375px */
            }

            .best-sellers .price {
                margin: 2.133vw 0; /* 8px based on 375px */
                font-size: 3.733vw; /* 14px based on 375px */
            }

            .best-sellers .old-price {
                margin-right: 2.133vw; /* 8px based on 375px */
            }

            .best-sellers .badge-sale {
                top: 2.667vw; /* 10px based on 375px */
                left: 2.667vw; /* 10px based on 375px */
                width: 13.067vw; /* 49px based on 375px */
                height: 6.667vw; /* 25px based on 375px */
                border-radius: 3.467vw; /* 13px based on 375px */
                font-size: 3.733vw; /* 14px based on 375px */
            }

            .best-sellers .badge-discount {
                top: 2.667vw; /* 10px based on 375px */
                left: 17.067vw; /* 64px (10px + 49px + 5px) based on 375px */
                width: 13.067vw; /* 49px based on 375px */
                height: 6.667vw; /* 25px based on 375px */
                border-radius: 3.467vw; /* 13px based on 375px */
                font-size: 3.733vw; /* 14px based on 375px */
            }

            .best-sellers .btn {
                width: 70.4vw; /* 264px based on 375px */
                height: 11.733vw; /* 44px based on 375px */
                border-radius: 16vw; /* 60px based on 375px */
                gap: 2.667vw; /* 10px based on 375px */
                margin: 0 auto;
            }
        }

        @media screen and (max-width: 768px) {
            .best-sellers .carousel-wrapper {
                flex-direction: column;
            }

            .best-sellers .carousel-arrow {
                display: none;
            }

            .best-sellers .carousel-container {
                flex-wrap: wrap;
                justify-content: center;
                overflow-x: auto;
            }

            .best-sellers .product-card {
                flex: 0 0 45%;
            }
        }
    </style>
</head>
<body>
<div class="best-sellers">
    <div class="section-title">{{ section.settings.title }}</div>
    <div class="carousel-wrapper">
        <button class="carousel-arrow prev" id="prevBtn">‹</button>
        <div class="carousel-container" id="carouselContainer">
            {% for block in section.blocks %}
                {% assign product = block.settings.product %}
                {% if product != blank %}
                    <div class="product-card">
                        {% comment %} 显示Sale标签和折扣 {% endcomment %}
                        {% if block.settings.show_sale and product.compare_at_price > product.price %}
                            <span class="badge-sale">Sale</span>
                            {% if block.settings.discount != blank %}
                                <span class="badge-discount">{{ block.settings.discount }}</span>
                            {% else %}
                                {% assign discount_amount = product.compare_at_price | minus: product.price %}
                                {% assign discount_percent = discount_amount | times: 100 | divided_by: product.compare_at_price %}
                                <span class="badge-discount">-{{ discount_percent }}%</span>
                            {% endif %}
                        {% endif %}

                        {% comment %} 产品图片 {% endcomment %}
                        <a href="{{ product.url }}">
                            <img src="{{ product.featured_image | img_url: '400x' }}" alt="{{ product.featured_image.alt | default: product.title }}">
                        </a>

                        {% comment %} 产品名称 {% endcomment %}
                        <a href="{{ product.url }}" class="product-name-link">
                            <div class="product-name">{{ product.title }}</div>
                        </a>

                        {% comment %} 产品价格 {% endcomment %}
                        <div class="price">
                            {% if product.compare_at_price > product.price %}
                                <span class="old-price">${{ product.compare_at_price | money_without_currency }}</span>
                                <span class="discount-price">${{ product.price | money_without_currency }}</span>
                            {% else %}
                                <span>${{ product.price | money_without_currency }}</span>
                            {% endif %}
                        </div>

                        {% comment %} 购买按钮 {% endcomment %}
                        <a href="{{ product.url }}" class="btn">Shop Now</a>
                    </div>
                {% endif %}
            {% endfor %}
        </div>
        <button class="carousel-arrow next" id="nextBtn">›</button>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const carouselContainer = document.getElementById('carouselContainer');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const productCards = carouselContainer.querySelectorAll('.product-card');

    // 响应式计算宽度
    function getResponsiveValues() {
        const screenWidth = window.innerWidth;
        let cardWidth, gap;

        if (screenWidth <= 480) {
            // 基于375px计算
            cardWidth = screenWidth * 0.8; // 80vw (300px)
            gap = screenWidth * 0.08; // 8vw
        } else if (screenWidth <= 768) {
            // 基于768px计算
            cardWidth = screenWidth * 0.58594; // 58.594vw (450px)
            gap = screenWidth * 0.03906; // 3.906vw
        } else {
            // 基于1920px计算
            cardWidth = screenWidth * 0.23438; // 23.438vw (450px)
            gap = screenWidth * 0.01563; // 1.563vw
        }

        return { cardWidth, gap };
    }

    const maxVisibleItems = 4; // 最多显示4个产品

    let currentIndex = 0;
    const totalItems = productCards.length;

    // 如果产品数量 <= 4，隐藏箭头
    if (totalItems <= maxVisibleItems) {
        prevBtn.classList.add('hidden');
        nextBtn.classList.add('hidden');
        return;
    }

    // 更新轮播位置
    function updateCarousel() {
        const { cardWidth, gap } = getResponsiveValues();
        const itemWidth = cardWidth + gap;
        const translateX = -currentIndex * itemWidth;
        carouselContainer.style.transform = `translateX(${translateX}px)`;

        // 更新按钮状态
        prevBtn.disabled = currentIndex === 0;
        nextBtn.disabled = currentIndex >= totalItems - maxVisibleItems;
    }

    // 上一个产品
    prevBtn.addEventListener('click', function() {
        if (currentIndex > 0) {
            currentIndex--;
            updateCarousel();
        }
    });

    // 下一个产品
    nextBtn.addEventListener('click', function() {
        if (currentIndex < totalItems - maxVisibleItems) {
            currentIndex++;
            updateCarousel();
        }
    });

    // 窗口大小变化时重新计算
    window.addEventListener('resize', function() {
        updateCarousel();
    });

    // 初始化
    updateCarousel();
});
</script>

</body>
</html>

{% schema %}
{
  "name": "Best Sellers",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Best Sellers"
    }
  ],
  "blocks": [
    {
      "type": "product_card",
      "name": "Product Card",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Select Product"
        },
        {
          "type": "checkbox",
          "id": "show_sale",
          "label": "Show Sale Badge",
          "default": false,
          "info": "Only shows if product has compare_at_price higher than price"
        },
        {
          "type": "text",
          "id": "discount",
          "label": "Custom Discount Label",
          "placeholder": "-23%",
          "info": "Leave blank to auto-calculate discount percentage"
        }
      ]
    }
  ],
  "max_blocks": 8,
  "presets": [
    {
      "name": "Best Sellers",
      "category": "Custom",
      "blocks": [
        {
          "type": "product_card",
          "settings": {
            "show_sale": true
          }
        },
        {
          "type": "product_card",
          "settings": {
            "show_sale": false
          }
        },
        {
          "type": "product_card",
          "settings": {
            "show_sale": true,
            "discount": "-30%"
          }
        }
      ]
    }
  ]
}
{% endschema %}
